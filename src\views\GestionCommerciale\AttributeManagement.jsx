import React, { useEffect, useState } from 'react';
import {
  fetchAttributeGroups,
  createAttributeGroup,
  updateAttributeGroup,
  deleteAttributeGroup,
  fetchAttributes,
  createAttribute,
  updateAttribute,
  deleteAttribute
} from '../../services/attributeService';
import { Container, Row, Col, Card, Button, Form, Table, Alert, Spinner, Badge, Modal, Tabs, Tab } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaLayerGroup, FaTags } from 'react-icons/fa';

export default function AttributeManagement() {
  // State for attribute groups
  const [attributeGroups, setAttributeGroups] = useState([]);
  const [groupLoading, setGroupLoading] = useState(false);

  // State for attributes
  const [attributes, setAttributes] = useState([]);
  const [attributeLoading, setAttributeLoading] = useState(false);

  // Modal states
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [showAttributeModal, setShowAttributeModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Modal action states
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [deleteTarget, setDeleteTarget] = useState({ type: '', id: null, name: '' });

  // Form states
  const [groupForm, setGroupForm] = useState({ name: '', description: '' });
  const [attributeForm, setAttributeForm] = useState({
    name: '',
    description: '',
    type: 'select',
    attribute_group_id: '',
    filtrable: false,
    comparable: false,
    obligatoire: false,
    sous_categories: []
  });
  // Editing states
  const [editingGroupId, setEditingGroupId] = useState(null);
  const [editingAttributeId, setEditingAttributeId] = useState(null);

  // Submission states
  const [submitting, setSubmitting] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState('groups');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Load attribute groups
  const loadAttributeGroups = async () => {
    setGroupLoading(true);
    setError('');
    try {
      const data = await fetchAttributeGroups();
      setAttributeGroups(data);
    } catch (e) {
      setError(e.message);
    }
    setGroupLoading(false);
  };

  // Load attributes
  const loadAttributes = async (groupId = null) => {
    setAttributeLoading(true);
    setError('');
    try {
      const data = await fetchAttributes(groupId);
      setAttributes(data);
    } catch (e) {
      setError(e.message);
    }
    setAttributeLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadAttributeGroups();
    loadAttributes();
  }, []);

  // Handle group form changes
  const handleGroupChange = (e) => {
    const { name, value } = e.target;
    setGroupForm({ ...groupForm, [name]: value });
  };

  // Handle attribute form changes
  const handleAttributeChange = (e) => {
    const { name, value } = e.target;
    setAttributeForm({ ...attributeForm, [name]: value });
  };

  // Modal handlers for groups
  const handleCreateGroup = () => {
    setModalAction('create');
    setEditingGroupId(null);
    setGroupForm({ name: '', description: '' });
    setShowGroupModal(true);
  };

  const handleEditGroup = (group) => {
    setModalAction('edit');
    setEditingGroupId(group.id);
    setGroupForm({
      name: group.name,
      description: group.description || ''
    });
    setShowGroupModal(true);
  };

  const handleGroupSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      if (modalAction === 'edit' && editingGroupId) {
        await updateAttributeGroup(editingGroupId, groupForm);
        setSuccess("Groupe d'attributs mis à jour avec succès");
      } else {
        await createAttributeGroup(groupForm);
        setSuccess("Groupe d'attributs créé avec succès");
      }

      setShowGroupModal(false);
      setGroupForm({ name: '', description: '' });
      setEditingGroupId(null);
      loadAttributeGroups();
    } catch (e) {
      setError(e.message);
    }

    setSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Modal handlers for attributes
  const handleCreateAttribute = () => {
    setModalAction('create');
    setEditingAttributeId(null);
    setAttributeForm({
      name: '',
      description: '',
      type: 'select',
      attribute_group_id: '',
      filtrable: false,
      comparable: false,
      obligatoire: false,
      sous_categories: []
    });
    setShowAttributeModal(true);
  };

  const handleEditAttribute = (attribute) => {
    setModalAction('edit');
    setEditingAttributeId(attribute.id);
    setAttributeForm({
      name: attribute.name,
      description: attribute.description || '',
      type: attribute.type || 'select',
      attribute_group_id: attribute.attribute_group_id || '',
      filtrable: attribute.filtrable || false,
      comparable: attribute.comparable || false,
      obligatoire: attribute.obligatoire || false,
      sous_categories: attribute.sous_categories || []
    });
    setShowAttributeModal(true);
  };

  const handleAttributeSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      if (modalAction === 'edit' && editingAttributeId) {
        await updateAttribute(editingAttributeId, attributeForm);
        setSuccess('Attribut mis à jour avec succès');
      } else {
        await createAttribute(attributeForm);
        setSuccess('Attribut créé avec succès');
      }

      setShowAttributeModal(false);
      setAttributeForm({
        name: '',
        description: '',
        type: 'select',
        attribute_group_id: '',
        filtrable: false,
        comparable: false,
        obligatoire: false,
        sous_categories: []
      });
      setEditingAttributeId(null);
      loadAttributes();
    } catch (e) {
      setError(e.message);
    }

    setSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Delete handlers
  const handleDeleteGroup = (group) => {
    setDeleteTarget({ type: 'group', id: group.id, name: group.name });
    setShowDeleteModal(true);
  };

  const handleDeleteAttribute = (attribute) => {
    setDeleteTarget({ type: 'attribute', id: attribute.id, name: attribute.name });
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    setSubmitting(true);
    setError('');

    try {
      const { type, id } = deleteTarget;

      if (type === 'group') {
        await deleteAttributeGroup(id);
        loadAttributeGroups();
        setSuccess("Groupe d'attributs supprimé avec succès");
      } else if (type === 'attribute') {
        await deleteAttribute(id);
        loadAttributes();
        setSuccess('Attribut supprimé avec succès');
      }
    } catch (e) {
      setError(e.message);
    }

    setSubmitting(false);
    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaTags className="me-2" />
          Gestion des Attributs
        </h2>
        <p className="text-muted">Gérez les attributs et leurs valeurs pour les produits.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="groups"
              title={
                <span>
                  <FaLayerGroup className="me-2" />
                  Groupes d'Attributs
                </span>
              }
            />
            <Tab
              eventKey="attributes"
              title={
                <span>
                  <FaTags className="me-2" />
                  Attributs
                </span>
              }
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Tab Content */}
      {activeTab === 'groups' && (
        <>
          {/* Groups List */}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des groupes d'attributs</h5>
              <div className="d-flex align-items-center gap-3">
                <div className="text-muted small">
                  {attributeGroups.length} groupe{attributeGroups.length !== 1 ? 's' : ''} d'attributs
                </div>
                <Button variant="primary" size="sm" onClick={handleCreateGroup} className="rounded-pill">
                  <FaPlus className="me-2" />
                  Ajouter un groupe
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {groupLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Chargement des groupes d'attributs...</p>
                </div>
              ) : attributeGroups.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <FaLayerGroup style={{ fontSize: '3rem' }} className="text-muted" />
                  </div>
                  <p className="text-muted">Aucun groupe d'attributs trouvé.</p>
                  <p className="text-muted">Utilisez le formulaire ci-dessus pour créer un nouveau groupe d'attributs.</p>
                </div>
              ) : (
                <Table hover responsive className="align-middle mb-0">
                  <thead>
                    <tr className="bg-light">
                      <th className="ps-3" style={{ width: '60px' }}>
                        ID
                      </th>
                      <th style={{ width: '25%' }}>Nom</th>
                      <th style={{ width: '35%' }}>Description</th>
                      <th style={{ width: '15%' }}>Attributs</th>
                      <th className="text-end pe-3" style={{ width: '25%' }}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {attributeGroups.map((group) => (
                      <tr key={group.id} className="border-bottom">
                        <td className="ps-3">{group.id}</td>
                        <td>
                          <span className="fw-medium">{group.name}</span>
                        </td>
                        <td>
                          <div className="text-truncate" style={{ maxWidth: '300px' }}>
                            {group.description || <span className="text-muted fst-italic">Aucune description</span>}
                          </div>
                        </td>
                        <td>
                          {group.attributes && group.attributes.length > 0 ? (
                            <Badge bg="info">{group.attributes.length} attributs</Badge>
                          ) : (
                            <span className="text-muted">Aucun attribut</span>
                          )}
                        </td>
                        <td className="text-end pe-3">
                          <Button
                            size="sm"
                            variant="outline-primary"
                            className="me-2 rounded-pill"
                            onClick={() => handleEditGroup(group)}
                            title="Éditer le groupe"
                          >
                            <FaPencilAlt className="me-1" /> Éditer
                          </Button>
                          <Button
                            size="sm"
                            variant="outline-danger"
                            className="rounded-pill"
                            onClick={() => handleDeleteGroup(group)}
                            title="Supprimer le groupe"
                          >
                            <FaTrashAlt className="me-1" /> Supprimer
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {activeTab === 'attributes' && (
        <>
          {/* Attributes List */}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h5 className="mb-0 fw-bold">Liste des attributs</h5>
              <div className="d-flex align-items-center gap-3">
                <div className="text-muted small">
                  {attributes.length} attribut{attributes.length !== 1 ? 's' : ''}
                </div>
                <Button variant="primary" size="sm" onClick={handleCreateAttribute} className="rounded-pill">
                  <FaPlus className="me-2" />
                  Ajouter un attribut
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="p-0">
              {attributeLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Chargement des attributs...</p>
                </div>
              ) : attributes.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <FaTags style={{ fontSize: '3rem' }} className="text-muted" />
                  </div>
                  <p className="text-muted">Aucun attribut trouvé.</p>
                  <p className="text-muted">Cliquez sur "Ajouter un attribut" pour créer un nouvel attribut.</p>
                </div>
              ) : (
                <Table hover responsive className="align-middle mb-0">
                  <thead>
                    <tr className="bg-light">
                      <th className="ps-3" style={{ width: '50px' }}>
                        ID
                      </th>
                      <th style={{ width: '20%' }}>Nom</th>
                      <th style={{ width: '10%' }}>Type</th>
                      <th style={{ width: '15%' }}>Groupe</th>
                      <th style={{ width: '20%' }}>Propriétés</th>
                      <th className="text-end pe-3" style={{ width: '25%' }}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {attributes.map((attribute) => {
                      const group = attributeGroups.find((g) => g.id === attribute.attribute_group_id);
                      return (
                        <tr key={attribute.id} className="border-bottom">
                          <td className="ps-3">{attribute.id}</td>
                          <td>
                            <span className="fw-medium">{attribute.name}</span>
                          </td>
                          <td>
                            <Badge bg="info" className="text-white">
                              {attribute.type === 'text' && 'Texte'}
                              {attribute.type === 'number' && 'Nombre'}
                              {attribute.type === 'boolean' && 'Booléen'}
                              {attribute.type === 'select' && 'Liste'}
                              {!['text', 'number', 'boolean', 'select'].includes(attribute.type) && attribute.type}
                            </Badge>
                          </td>
                          <td>
                            {group ? (
                              <Badge bg="primary" className="bg-opacity-10 text-primary">
                                {group.name}
                              </Badge>
                            ) : (
                              <span className="text-muted fst-italic">Aucun groupe</span>
                            )}
                          </td>
                          <td>
                            <div className="d-flex gap-1 flex-wrap">
                              {attribute.filtrable && (
                                <Badge bg="info" className="me-1">
                                  Filtrable
                                </Badge>
                              )}
                              {attribute.comparable && (
                                <Badge bg="success" className="me-1">
                                  Comparable
                                </Badge>
                              )}
                              {attribute.obligatoire && (
                                <Badge bg="warning" className="me-1">
                                  Obligatoire
                                </Badge>
                              )}
                              {!attribute.filtrable && !attribute.comparable && !attribute.obligatoire && (
                                <span className="text-muted fst-italic">Aucune</span>
                              )}
                            </div>
                          </td>
                          <td className="text-end pe-3">
                            <Button
                              size="sm"
                              variant="outline-primary"
                              className="me-2 rounded-pill"
                              onClick={() => handleEditAttribute(attribute)}
                              title="Éditer l'attribut"
                            >
                              <FaPencilAlt className="me-1" /> Éditer
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-danger"
                              className="rounded-pill"
                              onClick={() => handleDeleteAttribute(attribute)}
                              title="Supprimer l'attribut"
                            >
                              <FaTrashAlt className="me-1" /> Supprimer
                            </Button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {deleteTarget.type === 'group' && (
            <p>
              Êtes-vous sûr de vouloir supprimer le groupe d'attributs <strong>"{deleteTarget.name}"</strong> ?<br />
              <small className="text-muted">Cette action supprimera également tous les attributs associés à ce groupe.</small>
            </p>
          )}
          {deleteTarget.type === 'attribute' && (
            <p>
              Êtes-vous sûr de vouloir supprimer l'attribut <strong>"{deleteTarget.name}"</strong> ?<br />
              <small className="text-muted">Cette action supprimera également toutes les valeurs associées à cet attribut.</small>
            </p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)} disabled={submitting}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDelete} disabled={submitting}>
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Suppression...
              </>
            ) : (
              'Supprimer'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Group Modal */}
      <Modal show={showGroupModal} onHide={() => setShowGroupModal(false)} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <FaLayerGroup className="me-2" />
            {modalAction === 'edit' ? "Modifier le groupe d'attributs" : "Ajouter un nouveau groupe d'attributs"}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleGroupSubmit}>
          <Modal.Body>
            <Row className="g-3">
              <Col xs={12}>
                <Form.Group controlId="groupName">
                  <Form.Label className="fw-medium">Nom du groupe</Form.Label>
                  <Form.Control
                    name="name"
                    value={groupForm.name}
                    onChange={handleGroupChange}
                    placeholder="Ex: Caractéristiques techniques"
                    required
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
              <Col xs={12}>
                <Form.Group controlId="groupDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={groupForm.description}
                    onChange={handleGroupChange}
                    placeholder="Description du groupe d'attributs"
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowGroupModal(false)} disabled={submitting}>
              Annuler
            </Button>
            <Button type="submit" variant="primary" disabled={submitting}>
              {submitting ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  {modalAction === 'edit' ? 'Modification...' : 'Création...'}
                </>
              ) : (
                <>
                  {modalAction === 'edit' ? <FaPencilAlt className="me-2" /> : <FaPlus className="me-2" />}
                  {modalAction === 'edit' ? 'Modifier' : 'Créer'}
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Attribute Modal */}
      <Modal show={showAttributeModal} onHide={() => setShowAttributeModal(false)} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>
            <FaTags className="me-2" />
            {modalAction === 'edit' ? 'Modifier un attribut' : 'Ajouter un nouvel attribut'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleAttributeSubmit}>
          <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            <Row className="g-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="attributeName">
                  <Form.Label className="fw-medium">Nom de l'attribut</Form.Label>
                  <Form.Control
                    name="name"
                    value={attributeForm.name}
                    onChange={handleAttributeChange}
                    placeholder="Ex: Couleur"
                    required
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                <Form.Group controlId="attributeType">
                  <Form.Label className="fw-medium">Type d'attribut</Form.Label>
                  <Form.Select
                    name="type"
                    value={attributeForm.type}
                    onChange={handleAttributeChange}
                    required
                    disabled={submitting}
                    className="rounded-3 border-2"
                  >
                    <option value="text">Texte</option>
                    <option value="number">Nombre</option>
                    <option value="boolean">Booléen</option>
                    <option value="select">Liste de valeurs</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col xs={12}>
                <Form.Group controlId="attributeGroup">
                  <Form.Label className="fw-medium">Groupe d'attributs</Form.Label>
                  <Form.Select
                    name="attribute_group_id"
                    value={attributeForm.attribute_group_id}
                    onChange={handleAttributeChange}
                    disabled={submitting || attributeGroups.length === 0}
                    className="rounded-3 border-2"
                  >
                    <option value="">Aucun groupe</option>
                    {attributeGroups.map((group) => (
                      <option key={group.id} value={group.id}>
                        {group.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col xs={12}>
                <Form.Group controlId="attributeDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={attributeForm.description}
                    onChange={handleAttributeChange}
                    placeholder="Description de l'attribut"
                    disabled={submitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
              <Col xs={12}>
                <div className="border rounded-3 p-3 bg-light">
                  <h6 className="mb-3">Propriétés de l'attribut</h6>
                  <Row>
                    <Col xs={12} md={4}>
                      <Form.Group controlId="attributeFiltrable">
                        <Form.Check
                          type="checkbox"
                          label="Filtrable"
                          name="filtrable"
                          checked={attributeForm.filtrable}
                          onChange={(e) => setAttributeForm({ ...attributeForm, filtrable: e.target.checked })}
                          disabled={submitting}
                        />
                        <Form.Text className="text-muted">Permet de filtrer les produits par cet attribut</Form.Text>
                      </Form.Group>
                    </Col>
                    <Col xs={12} md={4}>
                      <Form.Group controlId="attributeComparable">
                        <Form.Check
                          type="checkbox"
                          label="Comparable"
                          name="comparable"
                          checked={attributeForm.comparable}
                          onChange={(e) => setAttributeForm({ ...attributeForm, comparable: e.target.checked })}
                          disabled={submitting}
                        />
                        <Form.Text className="text-muted">Permet de comparer les produits par cet attribut</Form.Text>
                      </Form.Group>
                    </Col>
                    <Col xs={12} md={4}>
                      <Form.Group controlId="attributeObligatoire">
                        <Form.Check
                          type="checkbox"
                          label="Obligatoire"
                          name="obligatoire"
                          checked={attributeForm.obligatoire}
                          onChange={(e) => setAttributeForm({ ...attributeForm, obligatoire: e.target.checked })}
                          disabled={submitting}
                        />
                        <Form.Text className="text-muted">Rend cet attribut obligatoire pour les produits</Form.Text>
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowAttributeModal(false)} disabled={submitting}>
              Annuler
            </Button>
            <Button type="submit" variant="primary" disabled={submitting}>
              {submitting ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  {modalAction === 'edit' ? 'Modification...' : 'Création...'}
                </>
              ) : (
                <>
                  {modalAction === 'edit' ? <FaPencilAlt className="me-2" /> : <FaPlus className="me-2" />}
                  {modalAction === 'edit' ? 'Modifier' : 'Créer'}
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Custom CSS for styling */}
      <style jsx="true">{`
        .nav-tabs-custom .nav-link {
          color: #495057;
          font-weight: 500;
          padding: 1rem 1.5rem;
          border-radius: 0;
          border: none;
          border-bottom: 3px solid transparent;
        }
        .nav-tabs-custom .nav-link.active {
          color: #2196f3;
          background: transparent;
          border-bottom: 3px solid #2196f3;
        }
        .nav-tabs-custom .nav-link:hover:not(.active) {
          border-bottom: 3px solid #e9ecef;
        }
      `}</style>
    </Container>
  );
}
